/*!
  Theme: Shapeshifter
  Author: <PERSON> (http://tybenz.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#102015;background:#f9f9f9}.hljs ::selection,.hljs::selection{background-color:#ababab;color:#102015}.hljs-comment{color:#555}.hljs-tag{color:#343434}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#102015}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#e92f2f}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#e09448}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#dddd13}.hljs-strong{font-weight:700;color:#dddd13}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#0ed839}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#23edda}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#3b48e3}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#f996e2}.hljs-emphasis{color:#f996e2;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#69542d}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}