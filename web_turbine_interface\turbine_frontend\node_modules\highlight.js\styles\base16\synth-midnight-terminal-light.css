pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Synth Midnight Terminal Light
  Author: <PERSON><PERSON><PERSON> (http://github.com/michael-ball/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme synth-midnight-terminal-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #dddfe0  Default Background
base01  #cfd1d2  Lighter Background (Used for status bars, line number and folding marks)
base02  #c1c3c4  Selection Background
base03  #a3a5a6  Comments, Invisibles, Line Highlighting
base04  #474849  Dark Foreground (Used for status bars)
base05  #28292a  Default Foreground, Caret, Delimiters, Operators
base06  #1a1b1c  Light Foreground (Not often used)
base07  #050608  Light Background (Not often used)
base08  #b53b50  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ea770d  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c9d364  Classes, Markup Bold, Search Text Background
base0B  #06ea61  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #42fff9  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #03aeff  Functions, Methods, Attribute IDs, Headings
base0E  #ea5ce2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #cd6320  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #28292a;
  background: #dddfe0
}
.hljs::selection,
.hljs ::selection {
  background-color: #c1c3c4;
  color: #28292a
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #a3a5a6 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #a3a5a6
}
/* base04 - #474849 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #474849
}
/* base05 - #28292a -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #28292a
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #b53b50
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ea770d
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c9d364
}
.hljs-strong {
  font-weight: bold;
  color: #c9d364
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #06ea61
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #42fff9
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #03aeff
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #ea5ce2
}
.hljs-emphasis {
  color: #ea5ce2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #cd6320
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}