# changelog

## 2.0.2

* Internal tidying up (change test runner, convert to JS)

## 2.0.1

* Robustify `this.remove()`, pass current index to walker functions ([#18](https://github.com/<PERSON>-<PERSON>/estree-walker/pull/18))

## 2.0.0

* Add an `asyncWalk` export ([#20](https://github.com/<PERSON>-<PERSON>/estree-walker/pull/20))
* Internal rewrite

## 1.0.1

* Relax node type to `BaseNode` ([#17](https://github.com/<PERSON>-Harris/estree-walker/pull/17))

## 1.0.0

* Don't cache child keys

## 0.9.0

* Add `this.remove()` method

## 0.8.1

* Fix pkg.files

## 0.8.0

* Adopt `estree` types

## 0.7.0

* Add a `this.replace(node)` method

## 0.6.1

* Only traverse nodes that exist and have a type ([#9](https://github.com/<PERSON>-<PERSON>/estree-walker/pull/9))
* Only cache keys for nodes with a type ([#8](https://github.com/<PERSON>-<PERSON>/estree-walker/pull/8))

## 0.6.0

* Fix walker context type
* Update deps, remove unncessary Bublé transformation

## 0.5.2

* Add types to package

## 0.5.1

* Prevent context corruption when `walk()` is called during a walk

## 0.5.0

* Export `childKeys`, for manually fixing in case of malformed ASTs

## 0.4.0

* Add TypeScript typings ([#3](https://github.com/Rich-Harris/estree-walker/pull/3))

## 0.3.1

* Include `pkg.repository` ([#2](https://github.com/Rich-Harris/estree-walker/pull/2))

## 0.3.0

* More predictable ordering

## 0.2.1

* Keep `context` shape

## 0.2.0

* Add ES6 build

## 0.1.3

* npm snafu

## 0.1.2

* Pass current prop and index to `enter`/`leave` callbacks

## 0.1.1

* First release
