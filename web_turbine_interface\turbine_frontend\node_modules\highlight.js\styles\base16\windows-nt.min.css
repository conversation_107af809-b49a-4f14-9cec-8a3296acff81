/*!
  Theme: Windows NT
  Author: <PERSON> (https://github.com/<PERSON>-<PERSON>)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:silver;background:#000}.hljs ::selection,.hljs::selection{background-color:#555;color:silver}.hljs-comment{color:grey}.hljs-tag{color:#a1a1a1}.hljs-operator,.hljs-punctuation,.hljs-subst{color:silver}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:red}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:olive}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#ff0}.hljs-strong{font-weight:700;color:#ff0}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#0f0}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#0ff}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#00f}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#f0f}.hljs-emphasis{color:#f0f;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:green}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}