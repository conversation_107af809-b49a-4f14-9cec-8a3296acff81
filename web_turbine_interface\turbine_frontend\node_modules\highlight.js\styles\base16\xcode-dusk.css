pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: XCode Dusk
  Author: <PERSON> (https://github.com/gonsie)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme xcode-dusk
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #282B35  Default Background
base01  #3D4048  Lighter Background (Used for status bars, line number and folding marks)
base02  #53555D  Selection Background
base03  #686A71  Comments, Invisibles, Line Highlighting
base04  #7E8086  Dark Foreground (Used for status bars)
base05  #939599  Default Foreground, Caret, Delimiters, Operators
base06  #A9AAAE  Light Foreground (Not often used)
base07  #BEBFC2  Light Background (Not often used)
base08  #B21889  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #786DC5  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #438288  Classes, Markup Bold, Search Text Background
base0B  #DF0002  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #00A0BE  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #790EAD  Functions, Methods, Attribute IDs, Headings
base0E  #B21889  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #C77C48  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #939599;
  background: #282B35
}
.hljs::selection,
.hljs ::selection {
  background-color: #53555D;
  color: #939599
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #686A71 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #686A71
}
/* base04 - #7E8086 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #7E8086
}
/* base05 - #939599 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #939599
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #B21889
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #786DC5
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #438288
}
.hljs-strong {
  font-weight: bold;
  color: #438288
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #DF0002
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #00A0BE
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #790EAD
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #B21889
}
.hljs-emphasis {
  color: #B21889;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #C77C48
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}